"""
H-Mamba-MIL: Hierarchical Mamba-based Multiple Instance Learning Framework
for Weakly Supervised Localization of Epileptogenic Foci from Interictal EEG

This module implements the core H-Mamba-MIL architecture with:
1. Mamba-2 Instance Encoder (First Layer)
2. Mamba-2 Temporal Aggregator (Second Layer) 
3. MIL Classification Head
4. Attention-based Localization

Author: AI Assistant
Date: 2024-08-20
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Tuple, Optional, Dict, Any
import math

# Import Mamba-2 from existing installation
try:
    from mamba_ssm import Mamba2
    MAMBA_AVAILABLE = True
except ImportError:
    print("Warning: mamba_ssm not available. Using LSTM fallback.")
    MAMBA_AVAILABLE = False


class Mamba2InstanceEncoder(nn.Module):
    """
    First Layer: Mamba-2 Instance Encoder
    
    Encodes individual EEG instances (2-second windows) into fixed-dimensional
    feature vectors using Mamba-2's efficient sequence modeling capabilities.
    
    Args:
        input_dim (int): Number of EEG channels
        d_model (int): Hidden dimension for Mamba-2
        d_state (int): State dimension for Mamba-2
        expand (int): Expansion factor for Mamba-2
        output_dim (int): Output feature dimension
    """
    
    def __init__(
        self,
        input_dim: int = 19,  # Standard EEG channels
        d_model: int = 256,
        d_state: int = 64,
        expand: int = 2,
        output_dim: int = 128,
        dropout: float = 0.1
    ):
        super().__init__()
        
        self.input_dim = input_dim
        self.d_model = d_model
        self.output_dim = output_dim
        
        # Input projection
        self.input_proj = nn.Linear(input_dim, d_model)
        
        # Mamba-2 layers for sequence modeling
        if MAMBA_AVAILABLE:
            self.mamba_layers = nn.ModuleList([
                Mamba2(
                    d_model=d_model,
                    d_state=d_state,
                    expand=expand,
                ) for _ in range(2)  # 2 Mamba-2 layers
            ])
        else:
            # Fallback to LSTM if Mamba not available
            self.mamba_layers = nn.ModuleList([
                nn.LSTM(d_model, d_model, batch_first=True)
                for _ in range(2)
            ])
        
        # Layer normalization
        self.layer_norms = nn.ModuleList([
            nn.LayerNorm(d_model) for _ in range(2)
        ])
        
        # Output projection
        self.output_proj = nn.Sequential(
            nn.Linear(d_model, d_model // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(d_model // 2, output_dim)
        )
        
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Forward pass for instance encoding
        
        Args:
            x: Input tensor of shape (batch_size, seq_len, input_dim)
               where seq_len is the number of time points in 2-second window
               
        Returns:
            Instance features of shape (batch_size, output_dim)
        """
        batch_size, seq_len, _ = x.shape
        
        # Input projection
        x = self.input_proj(x)  # (batch_size, seq_len, d_model)
        
        # Apply Mamba-2 layers with residual connections
        for i, (mamba_layer, layer_norm) in enumerate(zip(self.mamba_layers, self.layer_norms)):
            residual = x
            
            if MAMBA_AVAILABLE:
                x = mamba_layer(x)
            else:
                # LSTM fallback
                x, _ = mamba_layer(x)
            
            x = layer_norm(x + residual)
            x = self.dropout(x)
        
        # Global average pooling over sequence dimension
        x = torch.mean(x, dim=1)  # (batch_size, d_model)
        
        # Output projection
        instance_features = self.output_proj(x)  # (batch_size, output_dim)
        
        return instance_features


class Mamba2TemporalAggregator(nn.Module):
    """
    Second Layer: Mamba-2 Temporal Aggregator (Core Innovation)
    
    This is the key innovation of H-Mamba-MIL. It processes the sequence of
    instance features to:
    1. Learn normal EEG rhythm evolution patterns
    2. Identify instances that disrupt these patterns (potential IEDs)
    3. Generate attention weights for localization
    4. Produce bag-level representation for classification
    
    Args:
        input_dim (int): Dimension of instance features
        d_model (int): Hidden dimension for Mamba-2
        d_state (int): State dimension for Mamba-2
        expand (int): Expansion factor for Mamba-2
        num_classes (int): Number of output classes (2 for epilepsy/control)
    """
    
    def __init__(
        self,
        input_dim: int = 128,
        d_model: int = 256,
        d_state: int = 64,
        expand: int = 2,
        num_classes: int = 2,
        dropout: float = 0.1
    ):
        super().__init__()
        
        self.input_dim = input_dim
        self.d_model = d_model
        self.num_classes = num_classes
        
        # Input projection for instance features
        self.input_proj = nn.Linear(input_dim, d_model)
        
        # Mamba-2 layers for temporal aggregation
        if MAMBA_AVAILABLE:
            self.mamba_layers = nn.ModuleList([
                Mamba2(
                    d_model=d_model,
                    d_state=d_state,
                    expand=expand,
                ) for _ in range(3)  # 3 Mamba-2 layers for deeper temporal understanding
            ])
        else:
            # Fallback to LSTM
            self.mamba_layers = nn.ModuleList([
                nn.LSTM(d_model, d_model, batch_first=True)
                for _ in range(3)
            ])
        
        # Layer normalization
        self.layer_norms = nn.ModuleList([
            nn.LayerNorm(d_model) for _ in range(3)
        ])
        
        # Attention mechanism for instance weighting
        self.attention_proj = nn.Sequential(
            nn.Linear(d_model, d_model // 2),
            nn.Tanh(),
            nn.Linear(d_model // 2, 1)
        )
        
        # Bag-level representation
        self.bag_proj = nn.Sequential(
            nn.Linear(d_model, d_model // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(d_model // 2, d_model // 4)
        )
        
        # Classification head
        self.classifier = nn.Sequential(
            nn.Linear(d_model // 4, d_model // 8),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(d_model // 8, num_classes)
        )
        
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, instance_features: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        Forward pass for temporal aggregation and classification
        
        Args:
            instance_features: Tensor of shape (batch_size, num_instances, input_dim)
                              where num_instances is the number of 2-second windows in the bag
                              
        Returns:
            Tuple of:
            - bag_logits: Classification logits (batch_size, num_classes)
            - attention_weights: Instance attention weights (batch_size, num_instances)
            - bag_features: Bag-level features (batch_size, d_model//4)
        """
        batch_size, num_instances, _ = instance_features.shape
        
        # Input projection
        x = self.input_proj(instance_features)  # (batch_size, num_instances, d_model)
        
        # Apply Mamba-2 layers for temporal understanding
        for i, (mamba_layer, layer_norm) in enumerate(zip(self.mamba_layers, self.layer_norms)):
            residual = x
            
            if MAMBA_AVAILABLE:
                x = mamba_layer(x)
            else:
                # LSTM fallback
                x, _ = mamba_layer(x)
            
            x = layer_norm(x + residual)
            x = self.dropout(x)
        
        # Compute attention weights
        attention_scores = self.attention_proj(x)  # (batch_size, num_instances, 1)
        attention_weights = F.softmax(attention_scores.squeeze(-1), dim=1)  # (batch_size, num_instances)
        
        # Weighted aggregation for bag representation
        weighted_features = torch.sum(
            x * attention_weights.unsqueeze(-1), dim=1
        )  # (batch_size, d_model)
        
        # Generate bag-level features
        bag_features = self.bag_proj(weighted_features)  # (batch_size, d_model//4)
        
        # Classification
        bag_logits = self.classifier(bag_features)  # (batch_size, num_classes)
        
        return bag_logits, attention_weights, bag_features


class HMambaMIL(nn.Module):
    """
    Complete H-Mamba-MIL Framework

    This is the main model that combines:
    1. Mamba-2 Instance Encoder for encoding 2-second EEG windows
    2. Mamba-2 Temporal Aggregator for learning temporal patterns and classification

    The model implements Multiple Instance Learning (MIL) where:
    - Each "bag" is a complete EEG recording (4-5 minutes)
    - Each "instance" is a 2-second window from the recording
    - The model learns to classify bags (epilepsy vs control) while providing
      attention weights for weakly supervised localization of epileptogenic foci
    """

    def __init__(
        self,
        input_dim: int = 19,  # Number of EEG channels
        instance_encoder_config: Optional[Dict[str, Any]] = None,
        temporal_aggregator_config: Optional[Dict[str, Any]] = None,
        num_classes: int = 2
    ):
        super().__init__()

        # Default configurations
        if instance_encoder_config is None:
            instance_encoder_config = {
                'd_model': 256,
                'd_state': 64,
                'expand': 2,
                'output_dim': 128,
                'dropout': 0.1
            }

        if temporal_aggregator_config is None:
            temporal_aggregator_config = {
                'd_model': 256,
                'd_state': 64,
                'expand': 2,
                'dropout': 0.1
            }

        self.input_dim = input_dim
        self.num_classes = num_classes

        # First Layer: Instance Encoder
        self.instance_encoder = Mamba2InstanceEncoder(
            input_dim=input_dim,
            **instance_encoder_config
        )

        # Second Layer: Temporal Aggregator
        self.temporal_aggregator = Mamba2TemporalAggregator(
            input_dim=instance_encoder_config['output_dim'],
            num_classes=num_classes,
            **temporal_aggregator_config
        )

    def forward(self, bag_data: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        Forward pass for H-Mamba-MIL

        Args:
            bag_data: Input tensor of shape (batch_size, num_instances, seq_len, input_dim)
                     where:
                     - batch_size: number of bags (EEG recordings)
                     - num_instances: number of 2-second windows per bag
                     - seq_len: number of time points in each 2-second window
                     - input_dim: number of EEG channels

        Returns:
            Tuple of:
            - bag_logits: Classification logits (batch_size, num_classes)
            - attention_weights: Instance attention weights (batch_size, num_instances)
            - bag_features: Bag-level features (batch_size, feature_dim)
        """
        batch_size, num_instances, seq_len, input_dim = bag_data.shape

        # Reshape for instance encoding: (batch_size * num_instances, seq_len, input_dim)
        instances_flat = bag_data.view(-1, seq_len, input_dim)

        # First Layer: Encode each instance
        instance_features = self.instance_encoder(instances_flat)  # (batch_size * num_instances, output_dim)

        # Reshape back to bag format: (batch_size, num_instances, output_dim)
        instance_features = instance_features.view(batch_size, num_instances, -1)

        # Second Layer: Temporal aggregation and classification
        bag_logits, attention_weights, bag_features = self.temporal_aggregator(instance_features)

        return bag_logits, attention_weights, bag_features

    def get_attention_maps(self, bag_data: torch.Tensor) -> torch.Tensor:
        """
        Get attention weights for visualization and localization

        Args:
            bag_data: Input tensor of shape (batch_size, num_instances, seq_len, input_dim)

        Returns:
            attention_weights: Tensor of shape (batch_size, num_instances)
        """
        with torch.no_grad():
            _, attention_weights, _ = self.forward(bag_data)
        return attention_weights

    def predict_with_localization(self, bag_data: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        Make predictions with localization information

        Args:
            bag_data: Input tensor of shape (batch_size, num_instances, seq_len, input_dim)

        Returns:
            Dictionary containing:
            - predictions: Class predictions (batch_size,)
            - probabilities: Class probabilities (batch_size, num_classes)
            - attention_weights: Instance attention weights (batch_size, num_instances)
            - bag_features: Bag-level features (batch_size, feature_dim)
        """
        with torch.no_grad():
            bag_logits, attention_weights, bag_features = self.forward(bag_data)
            probabilities = F.softmax(bag_logits, dim=1)
            predictions = torch.argmax(probabilities, dim=1)

        return {
            'predictions': predictions,
            'probabilities': probabilities,
            'attention_weights': attention_weights,
            'bag_features': bag_features
        }


class MILLoss(nn.Module):
    """
    Multiple Instance Learning Loss Function

    Combines classification loss with optional attention regularization
    """

    def __init__(
        self,
        classification_weight: float = 1.0,
        attention_reg_weight: float = 0.01,
        use_attention_reg: bool = True
    ):
        super().__init__()

        self.classification_weight = classification_weight
        self.attention_reg_weight = attention_reg_weight
        self.use_attention_reg = use_attention_reg

        self.classification_loss = nn.CrossEntropyLoss()

    def forward(
        self,
        bag_logits: torch.Tensor,
        attention_weights: torch.Tensor,
        bag_labels: torch.Tensor
    ) -> Tuple[torch.Tensor, Dict[str, torch.Tensor]]:
        """
        Compute MIL loss

        Args:
            bag_logits: Classification logits (batch_size, num_classes)
            attention_weights: Instance attention weights (batch_size, num_instances)
            bag_labels: Bag labels (batch_size,)

        Returns:
            Tuple of:
            - total_loss: Combined loss
            - loss_dict: Dictionary of individual loss components
        """
        # Classification loss
        cls_loss = self.classification_loss(bag_logits, bag_labels)

        total_loss = self.classification_weight * cls_loss
        loss_dict = {'classification_loss': cls_loss}

        # Attention regularization (encourage sparsity)
        if self.use_attention_reg:
            # Entropy regularization to encourage focused attention
            attention_entropy = -torch.sum(
                attention_weights * torch.log(attention_weights + 1e-8), dim=1
            ).mean()

            # L2 regularization to encourage sparsity
            attention_l2 = torch.sum(attention_weights ** 2, dim=1).mean()

            attention_reg = attention_entropy + attention_l2
            total_loss += self.attention_reg_weight * attention_reg
            loss_dict['attention_reg'] = attention_reg

        return total_loss, loss_dict


# Utility functions for data processing
def create_mil_bags_from_eeg(
    eeg_data: np.ndarray,
    labels: np.ndarray,
    window_size: int = 512,  # 2 seconds at 256 Hz
    overlap: int = 128,      # 0.5 seconds overlap
    min_instances: int = 10  # Minimum instances per bag
) -> Tuple[list, list]:
    """
    Convert continuous EEG data into MIL bags

    Args:
        eeg_data: EEG data array of shape (n_samples, n_channels, n_timepoints)
        labels: Labels array of shape (n_samples,)
        window_size: Size of each instance window
        overlap: Overlap between consecutive windows
        min_instances: Minimum number of instances required per bag

    Returns:
        Tuple of (bags, bag_labels) where:
        - bags: List of bags, each bag is a tensor of shape (num_instances, window_size, n_channels)
        - bag_labels: List of bag labels
    """
    bags = []
    bag_labels = []

    for i, (recording, label) in enumerate(zip(eeg_data, labels)):
        # recording shape: (n_channels, n_timepoints)
        n_channels, n_timepoints = recording.shape

        # Create sliding windows
        instances = []
        step = window_size - overlap

        for start in range(0, n_timepoints - window_size + 1, step):
            end = start + window_size
            # Extract window and transpose to (window_size, n_channels)
            window = recording[:, start:end].T
            instances.append(window)

        # Only keep bags with sufficient instances
        if len(instances) >= min_instances:
            # Convert to tensor: (num_instances, window_size, n_channels)
            bag_tensor = torch.stack([torch.from_numpy(inst.astype(np.float32)) for inst in instances])
            bags.append(bag_tensor)
            bag_labels.append(label)

    return bags, bag_labels


def collate_mil_bags(batch):
    """
    Custom collate function for MIL bags with variable number of instances

    Args:
        batch: List of (bag, label) tuples

    Returns:
        Tuple of (padded_bags, labels, bag_sizes)
    """
    bags, labels = zip(*batch)

    # Find maximum number of instances
    max_instances = max(bag.shape[0] for bag in bags)

    # Pad bags to same number of instances
    padded_bags = []
    bag_sizes = []

    for bag in bags:
        num_instances, seq_len, n_channels = bag.shape
        bag_sizes.append(num_instances)

        if num_instances < max_instances:
            # Pad with zeros
            padding = torch.zeros(max_instances - num_instances, seq_len, n_channels)
            padded_bag = torch.cat([bag, padding], dim=0)
        else:
            padded_bag = bag

        padded_bags.append(padded_bag)

    # Stack into batch tensor
    batch_bags = torch.stack(padded_bags)
    batch_labels = torch.tensor(labels, dtype=torch.long)
    batch_sizes = torch.tensor(bag_sizes, dtype=torch.long)

    return batch_bags, batch_labels, batch_sizes
