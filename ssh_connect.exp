#!/usr/bin/expect -f

# SSH connection script with automatic password input
set timeout 30
set host "region.hefei.dbcloud.pro"
set port "53800"
set user "root"
set password "aFpEniE(6%cKp"

# Start SSH connection
spawn ssh -p $port $user@$host

# Handle different prompts
expect {
    "Are you sure you want to continue connecting (yes/no)?" {
        send "yes\r"
        exp_continue
    }
    "password:" {
        send "$password\r"
        exp_continue
    }
    "Password:" {
        send "$password\r"
        exp_continue
    }
    "$user@*" {
        # Successfully logged in
        puts "Successfully connected to remote server!"
        interact
    }
    timeout {
        puts "Connection timeout"
        exit 1
    }
    eof {
        puts "Connection failed"
        exit 1
    }
}
