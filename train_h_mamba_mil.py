"""
Training Script for H-Mamba-MIL
Trains the Hierarchical Mamba-based Multiple Instance Learning model for epilepsy classification

Author: AI Assistant
Date: 2024-08-20
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
import matplotlib.pyplot as plt
from sklearn.metrics import accuracy_score, precision_recall_fscore_support, confusion_matrix
import seaborn as sns
from tqdm import tqdm
import os
import json
from datetime import datetime

from h_mamba_mil import HMambaMIL, MILLoss
from data_processor import EEGDataProcessor, MILDataset, collate_mil_bags


class HMambaMILTrainer:
    """
    Trainer class for H-Mamba-MIL model
    """
    
    def __init__(
        self,
        model: HMambaMIL,
        device: str = 'cuda' if torch.cuda.is_available() else 'cpu',
        learning_rate: float = 1e-4,
        weight_decay: float = 1e-5
    ):
        self.model = model.to(device)
        self.device = device
        
        # Loss function
        self.criterion = MILLoss(
            classification_weight=1.0,
            attention_reg_weight=0.01,
            use_attention_reg=True
        )
        
        # Optimizer
        self.optimizer = optim.AdamW(
            self.model.parameters(),
            lr=learning_rate,
            weight_decay=weight_decay
        )
        
        # Learning rate scheduler
        self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer,
            mode='min',
            factor=0.5,
            patience=10,
            verbose=True
        )
        
        # Training history
        self.history = {
            'train_loss': [],
            'train_acc': [],
            'val_loss': [],
            'val_acc': [],
            'learning_rates': []
        }
        
    def train_epoch(self, train_loader: DataLoader) -> dict:
        """Train for one epoch"""
        self.model.train()
        
        total_loss = 0.0
        total_cls_loss = 0.0
        total_reg_loss = 0.0
        all_predictions = []
        all_labels = []
        
        pbar = tqdm(train_loader, desc="Training")
        
        for batch_idx, (bags, labels, bag_sizes) in enumerate(pbar):
            bags = bags.to(self.device)
            labels = labels.to(self.device)
            
            # Forward pass
            self.optimizer.zero_grad()
            bag_logits, attention_weights, bag_features = self.model(bags)
            
            # Compute loss
            loss, loss_dict = self.criterion(bag_logits, attention_weights, labels)
            
            # Backward pass
            loss.backward()
            
            # Gradient clipping
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            
            self.optimizer.step()
            
            # Statistics
            total_loss += loss.item()
            total_cls_loss += loss_dict['classification_loss'].item()
            if 'attention_reg' in loss_dict:
                total_reg_loss += loss_dict['attention_reg'].item()
            
            # Predictions
            predictions = torch.argmax(bag_logits, dim=1)
            all_predictions.extend(predictions.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())
            
            # Update progress bar
            pbar.set_postfix({
                'Loss': f"{loss.item():.4f}",
                'Acc': f"{accuracy_score(all_labels, all_predictions):.4f}"
            })
        
        # Calculate epoch metrics
        epoch_loss = total_loss / len(train_loader)
        epoch_acc = accuracy_score(all_labels, all_predictions)
        
        return {
            'loss': epoch_loss,
            'accuracy': epoch_acc,
            'cls_loss': total_cls_loss / len(train_loader),
            'reg_loss': total_reg_loss / len(train_loader)
        }
    
    def validate_epoch(self, val_loader: DataLoader) -> dict:
        """Validate for one epoch"""
        self.model.eval()
        
        total_loss = 0.0
        all_predictions = []
        all_labels = []
        all_attention_weights = []
        
        with torch.no_grad():
            for bags, labels, bag_sizes in tqdm(val_loader, desc="Validation"):
                bags = bags.to(self.device)
                labels = labels.to(self.device)
                
                # Forward pass
                bag_logits, attention_weights, bag_features = self.model(bags)
                
                # Compute loss
                loss, loss_dict = self.criterion(bag_logits, attention_weights, labels)
                
                total_loss += loss.item()
                
                # Predictions
                predictions = torch.argmax(bag_logits, dim=1)
                all_predictions.extend(predictions.cpu().numpy())
                all_labels.extend(labels.cpu().numpy())
                all_attention_weights.extend(attention_weights.cpu().numpy())
        
        # Calculate epoch metrics
        epoch_loss = total_loss / len(val_loader)
        epoch_acc = accuracy_score(all_labels, all_predictions)
        
        # Calculate detailed metrics
        precision, recall, f1, _ = precision_recall_fscore_support(
            all_labels, all_predictions, average='weighted'
        )
        
        return {
            'loss': epoch_loss,
            'accuracy': epoch_acc,
            'precision': precision,
            'recall': recall,
            'f1': f1,
            'predictions': all_predictions,
            'labels': all_labels,
            'attention_weights': all_attention_weights
        }
    
    def train(
        self,
        train_loader: DataLoader,
        val_loader: DataLoader,
        num_epochs: int = 100,
        save_dir: str = "results",
        model_name: str = "h_mamba_mil"
    ):
        """Complete training loop"""
        
        # Create save directory
        os.makedirs(save_dir, exist_ok=True)
        
        best_val_acc = 0.0
        best_epoch = 0
        
        print(f"Starting training on {self.device}")
        print(f"Model parameters: {sum(p.numel() for p in self.model.parameters()):,}")
        
        for epoch in range(num_epochs):
            print(f"\nEpoch {epoch+1}/{num_epochs}")
            print("-" * 50)
            
            # Train
            train_metrics = self.train_epoch(train_loader)
            
            # Validate
            val_metrics = self.validate_epoch(val_loader)
            
            # Update learning rate
            self.scheduler.step(val_metrics['loss'])
            
            # Update history
            self.history['train_loss'].append(train_metrics['loss'])
            self.history['train_acc'].append(train_metrics['accuracy'])
            self.history['val_loss'].append(val_metrics['loss'])
            self.history['val_acc'].append(val_metrics['accuracy'])
            self.history['learning_rates'].append(self.optimizer.param_groups[0]['lr'])
            
            # Print epoch results
            print(f"Train Loss: {train_metrics['loss']:.4f}, Train Acc: {train_metrics['accuracy']:.4f}")
            print(f"Val Loss: {val_metrics['loss']:.4f}, Val Acc: {val_metrics['accuracy']:.4f}")
            print(f"Val Precision: {val_metrics['precision']:.4f}, Val Recall: {val_metrics['recall']:.4f}, Val F1: {val_metrics['f1']:.4f}")
            
            # Save best model
            if val_metrics['accuracy'] > best_val_acc:
                best_val_acc = val_metrics['accuracy']
                best_epoch = epoch
                
                # Save model
                torch.save({
                    'epoch': epoch,
                    'model_state_dict': self.model.state_dict(),
                    'optimizer_state_dict': self.optimizer.state_dict(),
                    'val_acc': val_metrics['accuracy'],
                    'val_loss': val_metrics['loss']
                }, os.path.join(save_dir, f"{model_name}_best.pt"))
                
                print(f"New best model saved! Val Acc: {best_val_acc:.4f}")
            
            # Early stopping
            if epoch - best_epoch > 20:
                print(f"Early stopping at epoch {epoch+1}")
                break
        
        print(f"\nTraining completed!")
        print(f"Best validation accuracy: {best_val_acc:.4f} at epoch {best_epoch+1}")
        
        # Save training history
        with open(os.path.join(save_dir, f"{model_name}_history.json"), 'w') as f:
            json.dump(self.history, f, indent=2)
        
        # Plot training curves
        self.plot_training_curves(save_dir, model_name)
        
        # Generate final validation report
        self.generate_validation_report(val_metrics, save_dir, model_name)
    
    def plot_training_curves(self, save_dir: str, model_name: str):
        """Plot training curves"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # Loss curves
        axes[0, 0].plot(self.history['train_loss'], label='Train Loss')
        axes[0, 0].plot(self.history['val_loss'], label='Val Loss')
        axes[0, 0].set_title('Loss Curves')
        axes[0, 0].set_xlabel('Epoch')
        axes[0, 0].set_ylabel('Loss')
        axes[0, 0].legend()
        axes[0, 0].grid(True)
        
        # Accuracy curves
        axes[0, 1].plot(self.history['train_acc'], label='Train Acc')
        axes[0, 1].plot(self.history['val_acc'], label='Val Acc')
        axes[0, 1].set_title('Accuracy Curves')
        axes[0, 1].set_xlabel('Epoch')
        axes[0, 1].set_ylabel('Accuracy')
        axes[0, 1].legend()
        axes[0, 1].grid(True)
        
        # Learning rate
        axes[1, 0].plot(self.history['learning_rates'])
        axes[1, 0].set_title('Learning Rate')
        axes[1, 0].set_xlabel('Epoch')
        axes[1, 0].set_ylabel('Learning Rate')
        axes[1, 0].set_yscale('log')
        axes[1, 0].grid(True)
        
        # Remove empty subplot
        axes[1, 1].remove()
        
        plt.tight_layout()
        plt.savefig(os.path.join(save_dir, f"{model_name}_training_curves.png"), dpi=300, bbox_inches='tight')
        plt.close()
    
    def generate_validation_report(self, val_metrics: dict, save_dir: str, model_name: str):
        """Generate validation report with confusion matrix"""
        
        # Confusion matrix
        cm = confusion_matrix(val_metrics['labels'], val_metrics['predictions'])
        
        plt.figure(figsize=(8, 6))
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
                   xticklabels=['Control', 'Epilepsy'],
                   yticklabels=['Control', 'Epilepsy'])
        plt.title('Confusion Matrix - H-Mamba-MIL')
        plt.ylabel('True Label')
        plt.xlabel('Predicted Label')
        plt.savefig(os.path.join(save_dir, f"{model_name}_confusion_matrix.png"), dpi=300, bbox_inches='tight')
        plt.close()
        
        # Save detailed metrics
        report = {
            'accuracy': val_metrics['accuracy'],
            'precision': val_metrics['precision'],
            'recall': val_metrics['recall'],
            'f1': val_metrics['f1'],
            'confusion_matrix': cm.tolist(),
            'timestamp': datetime.now().isoformat()
        }
        
        with open(os.path.join(save_dir, f"{model_name}_validation_report.json"), 'w') as f:
            json.dump(report, f, indent=2)


def main():
    """Main training function"""
    
    # Configuration
    config = {
        'input_dim': 19,  # Number of EEG channels
        'batch_size': 8,
        'num_epochs': 100,
        'learning_rate': 1e-4,
        'weight_decay': 1e-5,
        'device': 'cuda' if torch.cuda.is_available() else 'cpu'
    }
    
    print("H-Mamba-MIL Training")
    print("=" * 50)
    print(f"Device: {config['device']}")
    print(f"Configuration: {config}")
    
    # Initialize data processor
    print("\nInitializing data processor...")
    data_processor = EEGDataProcessor()
    
    # Load or process data
    try:
        print("Attempting to load processed data...")
        train_bags, train_labels, test_bags, test_labels = data_processor.load_processed_data()
        print("✅ Loaded processed data successfully!")
    except:
        print("Processing raw data...")
        train_bags, train_labels, test_bags, test_labels = data_processor.load_and_process_dataset()
        data_processor.save_processed_data(train_bags, train_labels, test_bags, test_labels)
    
    # Create datasets and dataloaders
    train_dataset = MILDataset(train_bags, train_labels)
    test_dataset = MILDataset(test_bags, test_labels)
    
    train_loader = DataLoader(
        train_dataset,
        batch_size=config['batch_size'],
        shuffle=True,
        collate_fn=collate_mil_bags,
        num_workers=2
    )
    
    val_loader = DataLoader(
        test_dataset,
        batch_size=config['batch_size'],
        shuffle=False,
        collate_fn=collate_mil_bags,
        num_workers=2
    )
    
    print(f"Train batches: {len(train_loader)}")
    print(f"Validation batches: {len(val_loader)}")
    
    # Initialize model
    print("\nInitializing H-Mamba-MIL model...")
    model = HMambaMIL(input_dim=config['input_dim'])
    
    # Initialize trainer
    trainer = HMambaMILTrainer(
        model=model,
        device=config['device'],
        learning_rate=config['learning_rate'],
        weight_decay=config['weight_decay']
    )
    
    # Start training
    print("\nStarting training...")
    trainer.train(
        train_loader=train_loader,
        val_loader=val_loader,
        num_epochs=config['num_epochs'],
        save_dir="results",
        model_name="h_mamba_mil"
    )
    
    print("\n🎉 Training completed successfully!")


if __name__ == "__main__":
    main()
