# 研究计划：H-Mamba-MIL - 基于层次化Mamba与多示例学习的癫痫病灶弱监督定位

**版本:** 1.0
**日期:** 2024年7月5日

---

## 1. 项目标题

**H-Mamba-MIL: A Hierarchical Mamba-based Multiple Instance Learning Framework for Weakly Supervised Localization of Epileptogenic Foci from Interictal EEG**

(H-Mamba-MIL: 一个基于层次化Mamba与多示例学习的、用于发作间期EEG癫痫病灶弱监督定位的框架)

---

## 2. 摘要 (Abstract)

癫痫的精准诊断与治疗，特别是术前评估，高度依赖于对致痫区（epileptogenic zone）的精确定位。发作间期癫痫样放电（IEDs）是定位该区域的关键生物标志物，但其在长时程脑电图（EEG）中的手动标记耗时、主观且依赖专家经验。现有深度学习方法大多依赖于强标签（精确到毫秒的IED标记）或在弱监督场景下（仅有患者/健康标签）无法有效捕捉EEG信号内在的时序依赖性。为解决此问题，我们提出**H-Mamba-MIL**，一个新颖的、端到端的弱监督学习框架。该框架将长时程EEG的癫痫检测创新性地建模为一个**多示例学习（Multiple Instance Learning, MIL）**问题。我们设计了一个**层次化的Mamba-in-Mamba结构**：第一层Mamba-2作为高效的**示例编码器**，捕捉EEG短时窗内的局部动态特征；第二层Mamba-2作为创新的**时序聚合器**，通过学习EEG正常节律的演变模式来识别并聚合包含IED的关键示例。这种设计使模型不仅能“感知”时间，更能“理解”节律，从而在仅需患者级别标签的情况下，实现对癫痫患者的高精度分类和对关键IED事件的精确时空定位。我们期望H-Mamba-MIL能在方法学上为时间序列弱监督定位问题提供新的思路，并为临床癫痫诊断提供强大的自动化工具。

---

## 3. 引言与动机 (Introduction & Motivation)

*   **临床需求:** 癫痫影响全球数千万人，药物难治性癫痫患者需要通过手术切除致痫区。手术的成败关键在于病灶的术前精确定位。
*   **现有方法局限:**
    *   **金标准:** 长时程视频脑电图（LTV-EEG）是主要手段，但依赖神经电生理医生手动寻找稀疏、多样的IEDs，效率低下且主观性强。
    *   **传统监督学习:** 需要大量精确标记的IED数据，获取成本极高。
    *   **现有弱监督方法:** 虽然注意力机制已被应用，但大多将时间序列视为无序的特征集合，忽略了EEG信号至关重要的**时序依赖性**和**节律性**。例如，最新的TimeMIL (ICML 2024) 虽尝试解决此问题，但其采用的Transformer架构在处理超长序列时面临计算瓶颈，且其时间感知机制较为间接。
*   **我们的切入点:** 我们认为，癫痫检测的本质是一个“大海捞针”的弱监督问题，非常适合用MIL框架解决。同时，我们可以利用最新的状态空间模型Mamba-2的优势，以一种更根本、更优雅的方式来为EEG的长程时间动态建模，从而超越现有方法。

---

## 4. 相关工作 (Related Work)

1.  **EEG癫痫检测中的深度学习:** CNN, RNN/LSTM, Transformer已被广泛用于EEG分类。注意力机制常被用于提高性能，但多在标准监督学习框架下。
2.  **时间序列的多示例学习 (MIL for Time Series):**
    *   **MILLET (2023):** 成功将MIL框架用于通用时间序列分类，实现了内生的可解释性，证明了MIL在定位时间序列关键子序列（motifs）方面的潜力。
    *   **TimeMIL (ICML 2024):** 指出了传统MIL忽略时序依赖性的问题，并设计了时间感知的注意力池化机制，在多个数据集上取得了SOTA表现。这是我们最直接的比较和超越对象。

---

## 5. 提出的方法 (H-Mamba-MIL)

我们将整个30分钟的EEG录像视为一个**包 (Bag)**，将其切分为多个2秒的**示例 (Instance)**。我们的模型是一个端到端的层次化结构。

![H-Mamba-MIL 架构图](https://i.imgur.com/example.png)  <!-- 这是一个占位符，后续可以替换为真实的模型图 -->

### 5.1. 第一层: Mamba-2 示例编码器 (Instance Encoder)

*   **输入:** 单个EEG示例，维度为 `(C, T)` (C: 通道数, T: 采样点数)。
*   **核心:** 一个Mamba-2模型。
*   **任务:** 将输入的EEG窗口高效地编码为一个固定维度的特征向量 `h_i`。
*   **优势:** 利用Mamba-2的线性时间复杂度和状态空间模型的特性，能够以极高效率捕捉窗口内的局部动态特征，并天然地编码时序信息。

### 5.2. 第二层: Mamba-2 时序聚合器 (Temporal Aggregator) - **核心创新**

*   **输入:** 来自同一个包的所有示例的特征向量序列 `[h_1, h_2, ..., h_N]`。
*   **核心:** 第二个、更高层级的Mamba-2模型。
*   **任务:**
    1.  读取整个特征向量序列，学习正常EEG背景活动在分钟尺度上的**节律演变模式**。
    2.  基于其对整个序列的理解，输出一个最终的**包级别表示 (Bag Representation) `H_bag`**。
    3.  同时，根据每个示例 `h_i` 对正常节律的“扰动”程度，计算出一个**注意力权重 `a_i`**。
*   **优势:** 从根本上解决了TimeMIL中“独立同分布”假设的问题。它不是被动地“感知”时间，而是主动地“理解”节律，从而能更鲁棒地识别出破坏节律的异常事件。

### 5.3. 分类与定位

*   **分类:** 一个简单的全连接分类器接收 `H_bag`，输出该包（即该受试者）为“患者”或“健康”的概率。
*   **定位:** 模型训练完成后，`a_i` 的大小直接反映了第 `i` 个示例的重要性。通过将权重 `a_i` 映射回原始EEG的时间轴，即可实现对IEDs的**无监督时空定位**。

---

## 6. 创新与贡献 (Innovation & Contribution)

1.  **建模范式创新:** 首次将层次化状态空间模型（Mamba-2）引入时间序列的多示例学习框架，为弱监督下的EEG病灶定位提供了全新的、更强大的解决方案。
2.  **从“感知时间”到“理解节律”:** 我们的时序聚合器超越了现有方法对时间顺序的简单编码，转而学习和理解信号的内在节律动态，这更符合神经生理学的本质。
3.  **统一与优雅的架构:** 使用Mamba-2在微观（窗口内）和宏观（窗口间）两个时间尺度上进行统一建模，架构优雅且高效，特别适合处理EEG这类超长生理信号。
4.  **临床应用潜力:** 提供了一个端到端的自动化工具，仅需患者级别的粗粒度标签，即可实现对IEDs的精确定位，有望显著提升癫痫术前评估的效率和准确性。

---

## 7. 实验设计 (Experimental Design)

*   **数据集:** 您的癫痫患者与健康对照组的EEG和sMRI数据。
*   **预处理:**
    *   EEG: 带通滤波 (e.g., 1-70Hz), 陷波 (50/60Hz)。
    *   数据划分: 按受试者划分训练集、验证集、测试集。
    *   分包与分窗: 30分钟EEG为一个包，切分为2秒窗口，重叠1秒。
*   **评估指标:**
    *   **分类性能:** AUC-ROC, F1-Score, Accuracy。
    *   **定位性能:** 由于没有真实标签，将采用间接评估。例如，可以计算定位出的高权重区域与临床医生手动标记区域（如果可能获取少量）的重合度（IoU），或通过后续的BEM源定位结果与sMRI病变区域的一致性进行评估。
*   **基线模型 (Baselines):**
    1.  **Standard CNN/Transformer:** 对每个窗口进行分类，取最高概率作为患者诊断。
    2.  **Attention-based MIL:** 使用标准注意力池化的MIL框架。
    3.  **TimeMIL (SOTA):** 实现ICML 2024的TimeMIL作为最强基线。

---

## 8. 预期成果 (Expected Outcomes)

1.  H-Mamba-MIL将在患者分类任务上达到或超过基线模型，特别是在AUC-ROC指标上。
2.  模型定位出的高权重EEG片段，在视觉上与典型的IED波形高度吻合。
3.  通过BEM方法进行源定位后，定位出的脑区与患者sMRI显示的结构异常区域具有显著的相关性，从而验证我们方法的有效性。
4.  发表一篇具有影响力的高水平会议（如NeurIPS, ICLR, MICCAI）或期刊论文。

---
---

# 第二阶段研究计划：STM-UNet - 基于时空Mamba U-Net的癫痫手术区域智能预测

**版本:** 1.0
**日期:** 2024年7月5日

---

## 1. 项目标题

**STM-UNet: A Spatio-Temporal Mamba U-Net for Surgical Resection Area Prediction from 4D Epileptogenic Heatmaps and sMRI**

(STM-UNet: 一个基于时空Mamba U-Net的、融合4D致痫灶热力图与sMRI的手术切除区域预测模型)

---

## 2. 引言与动机 (Introduction & Motivation)

*   **临床转化桥梁:** 第一阶段的H-Mamba-MIL成功地从EEG中定位了致痫灶的“功能性”异常，并生成了4D时序热力图。然而，从功能定位到制定精准的“解剖学”手术切除范围，是临床决策中的巨大鸿沟。本阶段旨在跨越这一鸿沟。
*   **数据独特性:** 我们拥有的核心数据——**4D时序病灶热力图**——蕴含了病灶活动在空间和时间上的全部动态信息。简单地将其压缩为3D静态图会丢失宝贵的时间演化特征。
*   **研究一致性需求:** 为响应导师提出的“研究重心一致”的要求，我们拒绝采用传统的CNN（如3D U-Net）作为下游模型。我们致力于将Mamba/SSM作为统一的技术核心，贯穿从EEG分析到sMRI分割的整个研究流程。
*   **我们的目标:** 设计一个能直接处理和理解4D时空数据的全新分割模型**STM-UNet**，使其能够学习病灶动态与最终手术区域之间的复杂映射关系，从而为医生提供智能化的手术规划建议。

---

## 3. 提出的方法 (STM-UNet)

STM-UNet的核心是**解耦并深度融合时空信息**。它采用一个双分支编码器来分别处理解剖结构（空间）和病灶动态（时空），最终在解码器中融合信息以生成精准的分割结果。

![STM-UNet 架构图](https://i.imgur.com/example2.png) <!-- 这是一个占位符，后续可以替换为真实的模型图 -->

### 3.1. 空间编码器 (Anatomy Branch - "Where")

*   **输入:** 3D 静态sMRI图像。
*   **结构:** 一个标准的 **Vision Mamba (Vim) 编码器**。它由一系列Vim块和下采样层堆叠而成。
*   **作用:** 学习大脑精细的、静态的解剖学结构。它为模型提供高分辨率的结构上下文，是分割边界定位的基础。

### 3.2. 时空编码器 (Pathology Dynamics Branch - "What & When") - **核心创新**

*   **输入:** 4D 时序病灶热力图 (`T x D x H x W`)。
*   **结构:** 一个新颖的**时空Mamba (Spatio-Temporal Mamba, STM) 编码器**。
    1.  **空间建模 (Intra-frame Dynamics):** 对于4D热力图中的**每一个时间点 `t`**，我们都用一个共享权重的**Vim块**来捕捉该时刻病灶热力图的**空间分布特征**。这一步输出了一系列富含空间信息的时序特征图。
    2.  **时间建模 (Inter-frame Dynamics):** 对于**每一个空间位置 (patch)**，我们现在都有一个随时间变化的特征序列。我们再用一个**1D Mamba模型**沿着时间轴（`T`的维度）去扫描这个序列，从而捕捉该点病灶活动的**时间演化模式**（例如，是持续活跃，还是脉冲式爆发）。
*   **作用:** 这一分支专门学习病灶活动的动态时空特性，理解病灶是如何在时间和空间中演化的。

### 3.3. 特征融合与Mamba解码器 (Fusion & Decoder)

*   **特征融合:** 在U-Net的每个下采样层级，我们将“空间编码器”和“时空编码器”输出的特征图进行融合（例如，通过拼接后再送入一个Vim块进行深度融合）。这使得模型在每一层都能同时获得“这里是什么结构”和“这里发生了什么”的完整信息。
*   **Mamba解码器:** 融合后的特征将通过跳跃连接（Skip Connection）送入一个完全由**Vim块**构成的解码器，逐层上采样，并不断精炼特征，最终生成像素级的手术区域分割预测。

---

## 4. 创新与贡献 (Innovation & Contribution)

1.  **数据驱动的架构创新:** 首次提出STM-UNet，一个专为处理4D医学影像（特别是动态热力图）而设计的分割架构，完美利用了我们独特数据中的全部时空信息。
2.  **统一的研究叙事:** 成功地将Mamba/SSM模型从1D时序分析（H-Mamba-MIL）无缝拓展到4D时空分割（STM-UNet），形成了围绕“状态空间模型高效处理长程依赖”这一核心主题的、高度一致和新颖的研究闭环。
3.  **新颖的时空建模模块:** 设计了STM块，通过解耦和分层处理的方式，优雅地解决了4D数据中复杂的时空依赖建模问题，为4D医学图像分析提供了新的范式。
4.  **巨大的临床转化潜力:** 该模型直接面向手术规划这一关键临床需求，其输出结果可作为智能辅助工具，帮助医生更客观、更精准地确定手术切除范围，有望改善手术预后。

---

## 5. 实验设计 (Experimental Design)

*   **输入数据:**
    *   通道1: 3D sMRI 图像。
    *   通道2: 4D 时序病灶热力图。
*   **监督信号 (Ground Truth):** 真实的3D手术区域掩码。
*   **损失函数:** **Dice Loss + Focal Loss** 的组合，以应对前景（手术区域）和背景（非手术区域）之间严重的类别不平衡问题。
*   **评估指标:**
    *   **核心指标:** **Dice相似系数 (DSC)**，豪斯多夫距离 (Hausdorff Distance)。
    *   **辅助指标:** Volume Similarity, Surface Distance。
*   **基线模型 (Baselines):**
    1.  **3D U-Net:** 将4D热力图压缩为3D（如最大值投影），与sMRI拼接后送入标准的3D U-Net。
    2.  **ConvLSTM-UNet:** 一种经典的RNN+CNN混合模型，可以处理时序图像。
    3.  **VM-UNet:** 我们之前讨论过的、仅使用3D输入的Vision Mamba U-Net，作为消融实验的一部分，以证明处理时间维度的重要性。

---

## 6. 预期成果 (Expected Outcomes)

1.  STM-UNet在各项分割指标上（特别是DSC）将显著优于所有基线模型，证明其在处理4D数据上的优越性。
2.  消融实验将证明，我们设计的时空编码器是模型性能提升的关键。
3.  模型预测的手术区域与临床专家的金标准高度吻合，证明其临床应用的潜力。
4.  发表一篇顶级会议（如MICCAI, CVPR, ICCV）或期刊（如TMI, MedIA）论文，为4D医学图像智能分析领域贡献一个新的、强大的基准模型。