"""
Data Processing Module for H-Mamba-MIL
Handles EEG data loading, preprocessing, and MIL bag creation

Author: AI Assistant
Date: 2024-08-20
"""

import pandas as pd
import numpy as np
import torch
from torch.utils.data import Dataset, DataLoader
from typing import Tuple, List, Dict, Optional
import os
import gzip
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
import pickle


class EEGDataProcessor:
    """
    EEG Data Processor for H-Mamba-MIL
    
    Handles loading and preprocessing of EEG data from Guinea-Bissau and Nigeria datasets
    """
    
    def __init__(
        self,
        data_dir: str = "../1252141",
        sampling_rate: int = 256,
        window_size_seconds: float = 2.0,
        overlap_seconds: float = 0.5,
        min_instances_per_bag: int = 10
    ):
        self.data_dir = data_dir
        self.sampling_rate = sampling_rate
        self.window_size = int(window_size_seconds * sampling_rate)  # 512 samples for 2 seconds
        self.overlap = int(overlap_seconds * sampling_rate)  # 128 samples for 0.5 seconds
        self.min_instances_per_bag = min_instances_per_bag
        
        self.scaler = StandardScaler()
        self.is_fitted = False
        
    def load_metadata(self) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """Load metadata for both datasets"""
        gb_metadata = pd.read_csv(os.path.join(self.data_dir, "metadata_guineabissau.csv"))
        ng_metadata = pd.read_csv(os.path.join(self.data_dir, "metadata_nigeria.csv"))
        
        print(f"Guinea-Bissau metadata: {len(gb_metadata)} records")
        print(f"Nigeria metadata: {len(ng_metadata)} records")
        
        return gb_metadata, ng_metadata
    
    def load_eeg_recording(self, file_path: str) -> np.ndarray:
        """
        Load a single EEG recording from compressed CSV file
        
        Args:
            file_path: Path to the .csv.gz file
            
        Returns:
            EEG data as numpy array of shape (n_channels, n_timepoints)
        """
        try:
            with gzip.open(file_path, 'rt') as f:
                data = pd.read_csv(f)
            
            # Convert to numpy and transpose to (n_channels, n_timepoints)
            eeg_data = data.values.T
            
            return eeg_data.astype(np.float32)
            
        except Exception as e:
            print(f"Error loading {file_path}: {e}")
            return None
    
    def preprocess_recording(self, eeg_data: np.ndarray) -> np.ndarray:
        """
        Preprocess a single EEG recording
        
        Args:
            eeg_data: Raw EEG data of shape (n_channels, n_timepoints)
            
        Returns:
            Preprocessed EEG data
        """
        # Basic preprocessing
        # 1. Remove DC offset
        eeg_data = eeg_data - np.mean(eeg_data, axis=1, keepdims=True)
        
        # 2. Apply standardization (fit on first recording if not fitted)
        n_channels, n_timepoints = eeg_data.shape
        eeg_flat = eeg_data.reshape(-1, 1)
        
        if not self.is_fitted:
            self.scaler.fit(eeg_flat)
            self.is_fitted = True
        
        eeg_normalized = self.scaler.transform(eeg_flat).reshape(n_channels, n_timepoints)
        
        return eeg_normalized.astype(np.float32)
    
    def create_mil_bag(self, eeg_data: np.ndarray) -> Optional[torch.Tensor]:
        """
        Create MIL bag from EEG recording
        
        Args:
            eeg_data: Preprocessed EEG data of shape (n_channels, n_timepoints)
            
        Returns:
            MIL bag tensor of shape (num_instances, window_size, n_channels) or None if insufficient data
        """
        n_channels, n_timepoints = eeg_data.shape
        
        # Create sliding windows
        instances = []
        step = self.window_size - self.overlap
        
        for start in range(0, n_timepoints - self.window_size + 1, step):
            end = start + self.window_size
            # Extract window and transpose to (window_size, n_channels)
            window = eeg_data[:, start:end].T
            instances.append(torch.from_numpy(window))
        
        # Check if we have enough instances
        if len(instances) < self.min_instances_per_bag:
            return None
        
        # Stack instances into bag tensor
        bag_tensor = torch.stack(instances)  # (num_instances, window_size, n_channels)
        
        return bag_tensor
    
    def load_and_process_dataset(
        self,
        dataset_name: str = "both",  # "guinea_bissau", "nigeria", or "both"
        test_size: float = 0.2,
        random_state: int = 42
    ) -> Tuple[List[torch.Tensor], List[int], List[torch.Tensor], List[int]]:
        """
        Load and process complete dataset
        
        Args:
            dataset_name: Which dataset to load
            test_size: Fraction of data for testing
            random_state: Random seed for reproducibility
            
        Returns:
            Tuple of (train_bags, train_labels, test_bags, test_labels)
        """
        gb_metadata, ng_metadata = self.load_metadata()
        
        all_bags = []
        all_labels = []
        
        # Process Guinea-Bissau data
        if dataset_name in ["guinea_bissau", "both"]:
            print("Processing Guinea-Bissau dataset...")
            gb_dir = os.path.join(self.data_dir, "EEGs_Guinea-Bissau")
            
            for idx, row in gb_metadata.iterrows():
                file_path = os.path.join(gb_dir, f"{row['filename']}.csv.gz")
                
                if os.path.exists(file_path):
                    # Load and preprocess
                    eeg_data = self.load_eeg_recording(file_path)
                    if eeg_data is not None:
                        eeg_processed = self.preprocess_recording(eeg_data)
                        
                        # Create MIL bag
                        bag = self.create_mil_bag(eeg_processed)
                        if bag is not None:
                            all_bags.append(bag)
                            # Convert label: 1 for epilepsy, 0 for control
                            label = 1 if row['label'] == 'epilepsy' else 0
                            all_labels.append(label)
                            
                            if len(all_bags) % 10 == 0:
                                print(f"Processed {len(all_bags)} recordings...")
        
        # Process Nigeria data
        if dataset_name in ["nigeria", "both"]:
            print("Processing Nigeria dataset...")
            ng_dir = os.path.join(self.data_dir, "EEGs_Nigeria")
            
            for idx, row in ng_metadata.iterrows():
                file_path = os.path.join(ng_dir, f"{row['filename']}.csv.gz")
                
                if os.path.exists(file_path):
                    # Load and preprocess
                    eeg_data = self.load_eeg_recording(file_path)
                    if eeg_data is not None:
                        eeg_processed = self.preprocess_recording(eeg_data)
                        
                        # Create MIL bag
                        bag = self.create_mil_bag(eeg_processed)
                        if bag is not None:
                            all_bags.append(bag)
                            # Convert label: 1 for epilepsy, 0 for control
                            label = 1 if row['label'] == 'epilepsy' else 0
                            all_labels.append(label)
                            
                            if len(all_bags) % 10 == 0:
                                print(f"Processed {len(all_bags)} recordings...")
        
        print(f"Total processed bags: {len(all_bags)}")
        print(f"Label distribution: {np.bincount(all_labels)}")
        
        # Split into train/test
        train_bags, test_bags, train_labels, test_labels = train_test_split(
            all_bags, all_labels, test_size=test_size, random_state=random_state, stratify=all_labels
        )
        
        print(f"Train set: {len(train_bags)} bags")
        print(f"Test set: {len(test_bags)} bags")
        
        return train_bags, train_labels, test_bags, test_labels
    
    def save_processed_data(
        self,
        train_bags: List[torch.Tensor],
        train_labels: List[int],
        test_bags: List[torch.Tensor],
        test_labels: List[int],
        save_path: str = "processed_mil_data.pkl"
    ):
        """Save processed MIL data"""
        data = {
            'train_bags': train_bags,
            'train_labels': train_labels,
            'test_bags': test_bags,
            'test_labels': test_labels,
            'scaler': self.scaler,
            'config': {
                'sampling_rate': self.sampling_rate,
                'window_size': self.window_size,
                'overlap': self.overlap,
                'min_instances_per_bag': self.min_instances_per_bag
            }
        }
        
        with open(save_path, 'wb') as f:
            pickle.dump(data, f)
        
        print(f"Processed data saved to {save_path}")
    
    def load_processed_data(self, load_path: str = "processed_mil_data.pkl"):
        """Load processed MIL data"""
        with open(load_path, 'rb') as f:
            data = pickle.load(f)
        
        self.scaler = data['scaler']
        self.is_fitted = True
        
        config = data['config']
        self.sampling_rate = config['sampling_rate']
        self.window_size = config['window_size']
        self.overlap = config['overlap']
        self.min_instances_per_bag = config['min_instances_per_bag']
        
        return data['train_bags'], data['train_labels'], data['test_bags'], data['test_labels']


class MILDataset(Dataset):
    """
    PyTorch Dataset for MIL bags
    """
    
    def __init__(self, bags: List[torch.Tensor], labels: List[int]):
        self.bags = bags
        self.labels = labels
    
    def __len__(self):
        return len(self.bags)
    
    def __getitem__(self, idx):
        return self.bags[idx], self.labels[idx]


def collate_mil_bags(batch):
    """
    Custom collate function for MIL bags with variable number of instances
    """
    bags, labels = zip(*batch)
    
    # Find maximum number of instances
    max_instances = max(bag.shape[0] for bag in bags)
    
    # Pad bags to same number of instances
    padded_bags = []
    bag_sizes = []
    
    for bag in bags:
        num_instances, seq_len, n_channels = bag.shape
        bag_sizes.append(num_instances)
        
        if num_instances < max_instances:
            # Pad with zeros
            padding = torch.zeros(max_instances - num_instances, seq_len, n_channels)
            padded_bag = torch.cat([bag, padding], dim=0)
        else:
            padded_bag = bag
            
        padded_bags.append(padded_bag)
    
    # Stack into batch tensor
    batch_bags = torch.stack(padded_bags)
    batch_labels = torch.tensor(labels, dtype=torch.long)
    batch_sizes = torch.tensor(bag_sizes, dtype=torch.long)
    
    return batch_bags, batch_labels, batch_sizes
